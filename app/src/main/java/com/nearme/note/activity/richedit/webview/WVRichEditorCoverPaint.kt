/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVRichEditor.kt
 * Description    : WVRichEditor.kt
 * Version        : 1.0
 * Date           : 2023/11/14
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/11/14         1.0           create
 */
package com.nearme.note.activity.richedit.webview

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.nearme.note.paint.coverdoodle.CoverScaleRatio
import com.nearme.note.util.Injector
import com.nearme.note.util.WindowInsetsUtil
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webviewcoverpaint.container.impl.WebViewContainerImpl
import com.oplus.notes.webviewcoverpaint.container.web.BounceLayout
import com.oplus.notes.webviewcoverpaint.container.web.WVJBWebView
import com.oplus.notes.webviewcoverpaint.container.api.IWebViewContainerCoverpaint
import com.oplus.richtext.editor.RichEditor

class WVRichEditorCoverPaint : RichEditor {
    companion object {
        const val TAG = "WVRichEditorCoverPaint"
        private const val DEFAULT_WIDTH_FOLD = 403
        private const val DEFAULT_WIDTH_PHONE = 360
        private const val DEFAULT_WIDTH_PAD = 500
        private const val DEFAULT_WIDTH = 384
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle)

    val webViewContainer = Injector.injectFactory<IWebViewContainerCoverpaint>()
    lateinit var bounceView: BounceLayout
    private val defaultDensity: Float by lazy {
        val density = WindowInsetsUtil.getDefaultDensity().toFloat()
        val baseline = CSSPropertyManagerCoverPaint.BASELINE_DENSITY.toFloat()
        val result = if (density > 0 && baseline > 0) {
            density / baseline
        } else {
            // 如果获取密度失败，使用默认值 1.0f
            1.0f
        }
        AppLogger.BASIC.d(TAG, "defaultDensity calculated: density=$density, baseline=$baseline, result=$result")
        result
    }

    override fun initRichRecyclerView(backGroundView: View) {
        AppLogger.BASIC.d(TAG, "initRichRecyclerView: ")
        bounceView = findViewById(R.id.bounce_layout)
        wvScrollbarView = findViewById(R.id.wvScrollbarView)

        val container = webViewContainer ?: WebViewContainerImpl()

        val webView = container.createWebView(
            context,
            R.style.AppTheme_DayNight_WebView,
            bounceView,
            getWebViewLayoutParams(),
            backGroundView
        )
        if (webView != null) {
            mRichRecyclerView = webView
        }
    }

    override fun webViewLayoutInflated(): Boolean {
        return (mRichRecyclerView as? WVJBWebView)?.layoutInflated ?: false
    }

    private fun getWebViewLayoutParams(): LayoutParams {
        val webViewWidth = getWebViewDefaultWidth()
        val layoutParams = LayoutParams((webViewWidth * defaultDensity).toInt(), LayoutParams.MATCH_PARENT)
        layoutParams.topToTop = LayoutParams.PARENT_ID
        layoutParams.startToStart = LayoutParams.PARENT_ID
        layoutParams.endToEnd = LayoutParams.PARENT_ID
        return layoutParams
    }

    private fun getWebViewDefaultWidth(): Int {
        val webViewWidth = when (CoverScaleRatio.getDeviceType()) {
            CoverScaleRatio.IS_PAD -> DEFAULT_WIDTH_PAD
            CoverScaleRatio.IS_FOLD -> DEFAULT_WIDTH_FOLD
            CoverScaleRatio.IS_PHONE -> DEFAULT_WIDTH_PHONE
            CoverScaleRatio.IS_Dragonfly -> DEFAULT_WIDTH_PHONE
            else -> DEFAULT_WIDTH
        }
        return webViewWidth
    }

    override fun cancelScroll() {
        bounceView.cancelScroll()
    }
}